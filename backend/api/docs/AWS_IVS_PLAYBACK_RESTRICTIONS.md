# AWS IVS Playback Restrictions Configuration

This document explains how to configure AWS IVS playback restrictions for automatically securing new channels.

## Overview

When creating new AWS IVS channels, the system can automatically:
1. ✅ Enable channel authorization (`authorized: true`)
2. ✅ Create or reuse playback restriction policies
3. ✅ Associate policies with channels
4. ✅ Configure geographic and origin restrictions

## Environment Configuration

Add these environment variables to your `.env` file:

### Required for Private Channels
```bash
# AWS IVS Playback Keys (Required for private channels)
IVS_SHARED_PLAYBACK_PRIVATE_KEY=-----BEGIN EC PRIVATE KEY-----\nYour_Private_Key_Here\n-----END EC PRIVATE KEY-----
IVS_SHARED_PLAYBACK_KEY_NAME=shared-playback-keys
```

### Optional Playback Restrictions
```bash
# AWS IVS Playback Restriction Policy (Optional)
IVS_PLAYBACK_RESTRICTION_ENABLED=true
IVS_PLAYBACK_RESTRICTION_POLICY_NAME=default-restriction-policy
IVS_PLAYBACK_RESTRICTION_ALLOWED_COUNTRIES=US,CA,GB,DE,FR,AU
IVS_PLAYBACK_RESTRICTION_ALLOWED_ORIGINS=https://localhost:3443,https://yourdomain.com
IVS_PLAYBACK_RESTRICTION_STRICT_ORIGIN=false
```

## Configuration Options

### `IVS_PLAYBACK_RESTRICTION_ENABLED`
- **Type**: Boolean (`true`/`false`)
- **Default**: `false`
- **Description**: Enable automatic playback restriction policy creation and association

### `IVS_PLAYBACK_RESTRICTION_POLICY_NAME`
- **Type**: String
- **Default**: `default-restriction-policy`
- **Description**: Name for the playback restriction policy (reused if exists)

### `IVS_PLAYBACK_RESTRICTION_ALLOWED_COUNTRIES`
- **Type**: Comma-separated list
- **Default**: Empty (no country restrictions)
- **Description**: ISO 3166-1 alpha-2 country codes (e.g., `US,CA,GB,DE,FR`)
- **Example**: `US,CA,GB,DE,FR,AU,NZ,JP`

### `IVS_PLAYBACK_RESTRICTION_ALLOWED_ORIGINS`
- **Type**: Comma-separated list
- **Default**: `*` (all origins)
- **Description**: Allowed origins for CORS (use `*` for all origins)
- **Example**: `https://localhost:3443,https://yourdomain.com,https://app.yourdomain.com`

### `IVS_PLAYBACK_RESTRICTION_STRICT_ORIGIN`
- **Type**: Boolean (`true`/`false`)
- **Default**: `false`
- **Description**: Enable strict origin enforcement (recommended for production)

## How It Works

### 1. Channel Creation Process
When a new AWS IVS channel is created:

```typescript
// 1. Create channel with authorization enabled
const channel = await createChannel({
  name: channelId,
  authorized: true, // Always enabled for pay-per-view
  type: ChannelType.AdvancedHDChannelType,
});

// 2. Automatically configure playback restrictions (if enabled)
if (IVS_PLAYBACK_RESTRICTION_ENABLED === 'true') {
  await configurePlaybackRestrictionForChannel(channel.arn);
}
```

### 2. Policy Management
- **Reuses existing policies**: If a policy with the same name exists, it's reused
- **Creates new policies**: Only creates new policies when needed
- **Automatic association**: Policies are automatically associated with new channels

### 3. Geographic Restrictions
```bash
# Example: Allow only North America and Europe
IVS_PLAYBACK_RESTRICTION_ALLOWED_COUNTRIES=US,CA,MX,GB,DE,FR,IT,ES,NL,BE,CH,AT,SE,NO,DK,FI
```

### 4. Origin Restrictions
```bash
# Example: Allow specific domains only
IVS_PLAYBACK_RESTRICTION_ALLOWED_ORIGINS=https://yourdomain.com,https://app.yourdomain.com,https://mobile.yourdomain.com
```

## Production Recommendations

### Security Best Practices
```bash
# Production configuration example
IVS_PLAYBACK_RESTRICTION_ENABLED=true
IVS_PLAYBACK_RESTRICTION_POLICY_NAME=production-restriction-policy
IVS_PLAYBACK_RESTRICTION_ALLOWED_COUNTRIES=US,CA,GB,DE,FR,AU
IVS_PLAYBACK_RESTRICTION_ALLOWED_ORIGINS=https://yourdomain.com,https://app.yourdomain.com
IVS_PLAYBACK_RESTRICTION_STRICT_ORIGIN=true
```

### Development Configuration
```bash
# Development configuration example
IVS_PLAYBACK_RESTRICTION_ENABLED=true
IVS_PLAYBACK_RESTRICTION_POLICY_NAME=dev-restriction-policy
IVS_PLAYBACK_RESTRICTION_ALLOWED_COUNTRIES=
IVS_PLAYBACK_RESTRICTION_ALLOWED_ORIGINS=https://localhost:3443,http://localhost:3000
IVS_PLAYBACK_RESTRICTION_STRICT_ORIGIN=false
```

## Troubleshooting

### Common Issues

1. **Policy creation fails**
   - Check AWS credentials and permissions
   - Verify AWS region configuration
   - Ensure IVS service is available in your region

2. **Channel association fails**
   - Verify channel exists and is accessible
   - Check that `authorized: true` is set on the channel
   - Ensure policy ARN is valid

3. **Geographic restrictions not working**
   - Verify country codes are ISO 3166-1 alpha-2 format
   - Check that countries are comma-separated without spaces
   - Test from different geographic locations

4. **Origin restrictions not working**
   - Ensure origins include protocol (`https://` not just `domain.com`)
   - Check for trailing slashes and exact domain matching
   - Verify CORS configuration on your frontend

### Logging
The system logs all playback restriction activities:

```
[IvsService] Configuring playback restriction for channel: arn:aws:ivs:...
[IvsService] Found existing playback restriction policy: default-restriction-policy
[IvsService] Associated playback restriction policy with channel: arn:aws:ivs:... -> arn:aws:ivs:...
[IvsService] Successfully configured playback restriction for channel: arn:aws:ivs:...
```

## Manual Configuration

If you need to configure playback restrictions for existing channels:

```typescript
// Get the IVS service instance
const ivsService = app.get(IvsService);

// Configure restrictions for an existing channel
await ivsService.configurePlaybackRestrictionForChannel('your-channel-arn');
```

## AWS IVS Limits

- **Maximum policies**: 50 per account
- **Maximum countries per policy**: 250
- **Maximum origins per policy**: 10
- **Policy name length**: 1-128 characters

## Related Documentation

- [AWS IVS Playback Restrictions](https://docs.aws.amazon.com/ivs/latest/userguide/playback-restrictions.html)
- [AWS IVS Private Channels](https://docs.aws.amazon.com/ivs/latest/userguide/private-channels.html)
- [ISO 3166-1 Country Codes](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2)
