import {
  Injectable,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import {
  Channel,
  ChannelType,
  CreateChannelCommand,
  CreatePlaybackRestrictionPolicyCommand,
  DeleteChannelCommand,
  DeletePlaybackKeyPairCommand,
  DeletePlaybackRestrictionPolicyCommand,
  GetChannelCommand,
  GetPlaybackKeyPairCommand,
  GetPlaybackRestrictionPolicyCommand,
  GetStreamCommand,
  ImportPlaybackKeyPairCommand,
  IvsClient,
  ListChannelsCommand,
  ListPlaybackKeyPairsCommand,
  ListPlaybackRestrictionPoliciesCommand,
  PlaybackKeyPair,
  PlaybackRestrictionPolicy,
  StreamKey,
  StreamState,
  UpdateChannelCommand,
  UpdatePlaybackRestrictionPolicyCommand,
} from '@aws-sdk/client-ivs';
import { ConfigService } from '@nestjs/config';
import * as crypto from 'crypto';
import * as fs from 'fs';
import * as path from 'path';

// Define interfaces for our service
export interface IvsChannelResponse {
  rtmpUrl?: string | undefined;
  streamKey?: string | null | undefined;
  channelArn?: string | undefined;
  playbackUrl?: string | undefined;
  streamKeyArn?: string | null | undefined;
  channel?: Channel | undefined;
  streamKeyObj?: StreamKey | null | undefined;
  exist: boolean;
}

@Injectable()
export class IvsService {
  private readonly ivsClient: IvsClient;
  private readonly logger = new Logger(IvsService.name);

  // Shared playback keys for all channels (AWS IVS limit: 3 key pairs max)
  private sharedPlaybackKeys: {
    keyPairId: string;
    privateKey: string;
    publicKey: string;
    arn: string;
  } | null = null;

  constructor(private readonly configService: ConfigService) {
    const region = this.configService.get<string>('aws.region');
    const accessKeyId = this.configService.get<string>('aws.accessKeyId');
    const secretAccessKey = this.configService.get<string>(
      'aws.secretAccessKey',
    );

    this.ivsClient = new IvsClient({
      region,
      credentials: {
        accessKeyId: accessKeyId || '',
        secretAccessKey: secretAccessKey || '',
      },
    });

    // Initialize shared playback keys from environment variable
    this.initializeSharedPlaybackKeys();
  }

  /**
   * Initialize shared playback keys from environment configuration
   */
  private async initializeSharedPlaybackKeys(): Promise<void> {
    try {
      const privateKey = this.configService.get<string>('ivs.sharedPlaybackPrivateKey');
      const keyName = this.configService.get<string>('ivs.sharedPlaybackKeyName') || 'shared-playback-keys';

      this.logger.debug(`Private key from config: ${privateKey ? 'Found (' + privateKey.length + ' chars)' : 'Not found'}`);
      this.logger.debug(`Key name from config: ${keyName}`);

      if (!privateKey) {
        this.logger.warn('No shared playback private key configured in environment. Channels will need manual key configuration.');
        return;
      }

      this.logger.log('Initializing shared playback keys from environment configuration...');

      // Import the shared key pair
      const result = await this.importSharedPlaybackKeyPair(keyName, privateKey);

      if (result.success) {
        this.logger.log(`Successfully initialized shared playback keys: ${result.keyPairId}`);
        this.logger.log('All new channels will automatically use shared playback keys for authorization.');
      } else {
        this.logger.error(`Failed to initialize shared playback keys: ${result.error}`);
        this.logger.error('Channels will need manual key configuration until this is resolved.');
      }
    } catch (error) {
      this.logger.error(`Error initializing shared playback keys: ${error instanceof Error ? error.message : 'Unknown error'}`);
      this.logger.error('Channels will need manual key configuration until this is resolved.');
    }
  }

  /**
   * Create an AWS IVS channel
   * @param channelId The Neo4j channel ID to use as the AWS IVS channel name
   * @returns The created channel details including ingest endpoint and stream key
   */
  async createChannel(channelId: string): Promise<IvsChannelResponse> {
    try {
      // Check if channel already exists
      const listCommand = new ListChannelsCommand({
        filterByName: channelId,
        maxResults: 1,
      });
      const listResponse = await this.ivsClient.send(listCommand);

      if (listResponse.channels?.length) {
        this.logger.log(`Channel ${channelId} already exists in AWS IVS`);
        return {
          exist: true,
        };
      }

      // Create new channel with authorization enabled for paid content
      const command = new CreateChannelCommand({
        name: channelId,
        authorized: true, // Enable authorization for paid content
        type: ChannelType.AdvancedHDChannelType,
      });

      const response = await this.ivsClient.send(command);

      // Configure playback restriction policy if enabled in environment
      if (response.channel?.arn) {
        await this.configurePlaybackRestrictionForChannel(response.channel.arn);
      }

      return {
        exist: false,
        rtmpUrl: response.channel?.ingestEndpoint,
        streamKey: response.streamKey?.value,
        channelArn: response.channel?.arn,
        playbackUrl: response.channel?.playbackUrl,
        streamKeyArn: response.streamKey?.arn,
        channel: response.channel,
        streamKeyObj: response.streamKey,
      };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Failed to create IVS channel: ${errorMessage}`,
        errorStack,
      );
      throw new InternalServerErrorException(
        `Failed to create IVS channel: ${errorMessage}`,
      );
    }
  }

  /**
   * Delete an AWS IVS channel
   * @param channelId The Neo4j channel ID used as the AWS IVS channel name
   * @returns True if deleted successfully
   */
  async deleteChannel(channelId: string): Promise<boolean> {
    try {
      const listCommand = new ListChannelsCommand({
        filterByName: channelId,
        maxResults: 1,
      });
      const listResponse = await this.ivsClient.send(listCommand);

      if (listResponse.channels?.length) {
        const channelArn = listResponse.channels[0].arn;
        const deleteCommand = new DeleteChannelCommand({
          arn: channelArn,
        });
        await this.ivsClient.send(deleteCommand);
        return true;
      }
      return true; // Channel doesn't exist, return true
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Failed to delete IVS channel: ${errorMessage}`,
        errorStack,
      );
      if (error instanceof Error && error.name === 'ConflictException') {
        throw new InternalServerErrorException(
          'Channel is live. Stop the stream and wait for Stream End event before deleting.',
        );
      }
      throw new InternalServerErrorException(
        `Failed to delete IVS channel: ${errorMessage}`,
      );
    }
  }

  /**
   * Check if a channel is currently streaming
   * @param channelArn The AWS IVS channel ARN
   * @returns True if the channel is live
   */
  async isLive(channelArn: string): Promise<boolean> {
    try {
      const command = new GetStreamCommand({
        channelArn: channelArn,
      });
      const response = await this.ivsClient.send(command);
      return response.stream?.state === StreamState.StreamLive;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Failed to check stream status: ${errorMessage}`,
        errorStack,
      );
      return false;
    }
  }



  /**
   * Get channel information
   * @param channelArn The AWS IVS channel ARN
   * @returns Channel information
   */
  async getChannelInfo(channelArn: string): Promise<Channel | null> {
    try {
      const command = new GetChannelCommand({
        arn: channelArn,
      });
      const response = await this.ivsClient.send(command);
      return response.channel || null;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Failed to get channel info: ${errorMessage}`,
        errorStack,
      );
      return null;
    }
  }

  /**
   * List all playback key pairs
   * @returns List of playback key pairs
   */
  async listPlaybackKeyPairs(): Promise<PlaybackKeyPair[]> {
    try {
      const command = new ListPlaybackKeyPairsCommand({
        maxResults: 50, // AWS IVS limit
      });
      const response = await this.ivsClient.send(command);
      return response.keyPairs || [];
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Failed to list playback key pairs: ${errorMessage}`,
        errorStack,
      );
      return [];
    }
  }

  /**
   * Get a specific playback key pair
   * @param arn The playback key pair ARN
   * @returns Playback key pair details
   */
  async getPlaybackKeyPair(arn: string): Promise<PlaybackKeyPair | null> {
    try {
      const command = new GetPlaybackKeyPairCommand({
        arn: arn,
      });
      const response = await this.ivsClient.send(command);
      return response.keyPair || null;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Failed to get playback key pair: ${errorMessage}`,
        errorStack,
      );
      return null;
    }
  }

  /**
   * Check if a channel has authorization enabled
   * @param channelArn The AWS IVS channel ARN
   * @returns True if the channel has authorization enabled
   */
  async isChannelAuthorizationEnabled(channelArn: string): Promise<boolean> {
    try {
      const channelInfo = await this.getChannelInfo(channelArn);
      return channelInfo?.authorized || false;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(
        `Failed to check channel authorization status: ${errorMessage}`,
      );
      return false;
    }
  }

  /**
   * Enable authorization for a channel
   * @param channelArn The AWS IVS channel ARN
   * @returns True if authorization was enabled successfully
   */
  async enableChannelAuthorization(channelArn: string): Promise<boolean> {
    try {
      // First get the current channel configuration
      const getCommand = new GetChannelCommand({
        arn: channelArn,
      });
      const getResponse = await this.ivsClient.send(getCommand);

      if (!getResponse.channel) {
        this.logger.error(`Channel not found: ${channelArn}`);
        return false;
      }

      // Update the channel to enable authorization
      const updateCommand = new UpdateChannelCommand({
        arn: channelArn,
        name: getResponse.channel.name,
        authorized: true, // Enable authorization
        type: getResponse.channel.type,
      });

      await this.ivsClient.send(updateCommand);
      this.logger.log(`Authorization enabled for channel: ${channelArn}`);
      return true;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Failed to enable channel authorization: ${errorMessage}`,
        errorStack,
      );
      return false;
    }
  }

  /**
   * Disable authorization for a channel
   * @param channelArn The AWS IVS channel ARN
   * @returns True if authorization was disabled successfully
   */
  async disableChannelAuthorization(channelArn: string): Promise<boolean> {
    try {
      // First get the current channel configuration
      const getCommand = new GetChannelCommand({
        arn: channelArn,
      });
      const getResponse = await this.ivsClient.send(getCommand);

      if (!getResponse.channel) {
        this.logger.error(`Channel not found: ${channelArn}`);
        return false;
      }

      // Update the channel to disable authorization
      const updateCommand = new UpdateChannelCommand({
        arn: channelArn,
        name: getResponse.channel.name,
        authorized: false, // Disable authorization
        type: getResponse.channel.type,
      });

      await this.ivsClient.send(updateCommand);
      this.logger.log(`Authorization disabled for channel: ${channelArn}`);
      return true;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Failed to disable channel authorization: ${errorMessage}`,
        errorStack,
      );
      return false;
    }
  }



  /**
   * Delete a playback key pair
   * @param arn The playback key pair ARN
   * @returns True if deleted successfully
   */
  async deletePlaybackKeyPair(arn: string): Promise<boolean> {
    try {
      const command = new DeletePlaybackKeyPairCommand({
        arn: arn,
      });

      await this.ivsClient.send(command);
      this.logger.log(`Deleted playback key pair: ${arn}`);
      return true;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Failed to delete playback key pair: ${errorMessage}`,
        errorStack,
      );
      return false;
    }
  }



  /**
   * Get available playback key pairs (for informational purposes)
   * Note: AWS IVS doesn't return private keys for security reasons
   * @returns List of available key pair ARNs and names
   */
  async getAvailablePlaybackKeyPairs(): Promise<Array<{
    arn: string;
    name?: string;
    keyPairId: string;
  }>> {
    try {
      const keyPairs = await this.listPlaybackKeyPairs();

      return keyPairs.map(keyPair => ({
        arn: keyPair.arn || '',
        name: keyPair.name,
        keyPairId: keyPair.arn?.split('/').pop() || '',
      })).filter(kp => kp.arn && kp.keyPairId);
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(
        `Failed to get available playback key pairs: ${errorMessage}`,
      );
      return [];
    }
  }

  /**
   * Configure shared playback keys for all channels
   * @param keyPairId The AWS IVS key pair ID
   * @param privateKey The private key content (PEM format)
   * @param publicKey The public key content (PEM format)
   * @param arn The AWS IVS key pair ARN
   */
  configureSharedPlaybackKeys(
    keyPairId: string,
    privateKey: string,
    publicKey: string,
    arn: string,
  ): void {
    this.sharedPlaybackKeys = {
      keyPairId,
      privateKey,
      publicKey,
      arn,
    };
    this.logger.log(`Configured shared playback keys with key pair ID: ${keyPairId}`);
  }

  /**
   * Get the shared playback keys
   * @returns Shared playback keys or null if not configured
   */
  getSharedPlaybackKeys(): {
    keyPairId: string;
    privateKey: string;
    publicKey: string;
    arn: string;
  } | null {
    return this.sharedPlaybackKeys;
  }

  /**
   * Check if shared playback keys are configured
   * @returns True if shared keys are available
   */
  hasSharedPlaybackKeys(): boolean {
    return this.sharedPlaybackKeys !== null;
  }

  /**
   * Create or get a playback restriction policy
   * @param policyName Name for the policy
   * @param allowedCountries Array of allowed country codes (ISO 3166-1 alpha-2)
   * @param allowedOrigins Array of allowed origins for CORS
   * @param strictOriginEnforcement Whether to enforce strict origin checking
   * @returns The policy ARN
   */
  async createOrGetPlaybackRestrictionPolicy(
    policyName: string,
    allowedCountries: string[] = [],
    allowedOrigins: string[] = ['*'],
    strictOriginEnforcement: boolean = false
  ): Promise<string | null> {
    try {
      // First, try to find an existing policy with the same name
      const listCommand = new ListPlaybackRestrictionPoliciesCommand({
        maxResults: 50,
      });
      const listResponse = await this.ivsClient.send(listCommand);

      const existingPolicy = listResponse.playbackRestrictionPolicies?.find(
        policy => policy.name === policyName
      );

      if (existingPolicy?.arn) {
        this.logger.log(`Found existing playback restriction policy: ${policyName} (${existingPolicy.arn})`);
        return existingPolicy.arn;
      }

      // Create new policy if it doesn't exist
      const createCommand = new CreatePlaybackRestrictionPolicyCommand({
        name: policyName,
        allowedCountries: allowedCountries.length > 0 ? allowedCountries : undefined,
        allowedOrigins: allowedOrigins,
        enableStrictOriginEnforcement: strictOriginEnforcement,
      });

      const createResponse = await this.ivsClient.send(createCommand);

      if (createResponse.playbackRestrictionPolicy?.arn) {
        this.logger.log(`Created new playback restriction policy: ${policyName} (${createResponse.playbackRestrictionPolicy.arn})`);
        return createResponse.playbackRestrictionPolicy.arn;
      }

      this.logger.error(`Failed to create playback restriction policy: ${policyName}`);
      return null;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`Failed to create or get playback restriction policy: ${errorMessage}`);
      return null;
    }
  }

  /**
   * Associate a playback restriction policy with a channel
   * @param channelArn The AWS IVS channel ARN
   * @param policyArn The playback restriction policy ARN
   * @returns True if association was successful
   */
  async associatePlaybackRestrictionPolicy(
    channelArn: string,
    policyArn: string
  ): Promise<boolean> {
    try {
      // Get current channel configuration
      const getCommand = new GetChannelCommand({ arn: channelArn });
      const getResponse = await this.ivsClient.send(getCommand);

      if (!getResponse.channel) {
        this.logger.error(`Channel not found: ${channelArn}`);
        return false;
      }

      // Update channel with playback restriction policy
      const updateCommand = new UpdateChannelCommand({
        arn: channelArn,
        name: getResponse.channel.name,
        authorized: true, // Must be true for playback restrictions
        type: getResponse.channel.type,
        playbackRestrictionPolicyArn: policyArn,
      });

      await this.ivsClient.send(updateCommand);
      this.logger.log(`Associated playback restriction policy with channel: ${channelArn} -> ${policyArn}`);
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`Failed to associate playback restriction policy: ${errorMessage}`);
      return false;
    }
  }

  /**
   * Configure playback restriction for a channel based on environment settings
   * @param channelArn The AWS IVS channel ARN
   * @returns True if configuration was successful
   */
  private async configurePlaybackRestrictionForChannel(channelArn: string): Promise<boolean> {
    try {
      const playbackRestrictionConfig = this.configService.get('ivs.playbackRestriction');

      if (!playbackRestrictionConfig?.enabled) {
        this.logger.debug(`Playback restriction not enabled for channel: ${channelArn}`);
        return true; // Not an error, just not configured
      }

      this.logger.log(`Configuring playback restriction for channel: ${channelArn}`);

      // Create or get the playback restriction policy
      const policyArn = await this.createOrGetPlaybackRestrictionPolicy(
        playbackRestrictionConfig.policyName,
        playbackRestrictionConfig.allowedCountries,
        playbackRestrictionConfig.allowedOrigins,
        playbackRestrictionConfig.strictOriginEnforcement
      );

      if (!policyArn) {
        this.logger.error(`Failed to create/get playback restriction policy for channel: ${channelArn}`);
        return false;
      }

      // Associate the policy with the channel
      const success = await this.associatePlaybackRestrictionPolicy(channelArn, policyArn);

      if (success) {
        this.logger.log(`Successfully configured playback restriction for channel: ${channelArn}`);
      } else {
        this.logger.error(`Failed to associate playback restriction policy with channel: ${channelArn}`);
      }

      return success;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`Error configuring playback restriction for channel ${channelArn}: ${errorMessage}`);
      return false;
    }
  }

  /**
   * Verify that a key pair exists in AWS IVS
   * @param keyPairId The key pair ID to verify
   * @returns True if the key pair exists
   */
  async verifyKeyPairExists(keyPairId: string): Promise<boolean> {
    try {
      const keyPairs = await this.listPlaybackKeyPairs();
      const exists = keyPairs.some(kp => kp.arn?.endsWith(`/${keyPairId}`));
      this.logger.debug(`Key pair ${keyPairId} exists in AWS: ${exists}`);
      return exists;
    } catch (error) {
      this.logger.error(`Error verifying key pair ${keyPairId}:`, error);
      return false;
    }
  }

  /**
   * Clear shared playback keys
   */
  clearSharedPlaybackKeys(): void {
    this.sharedPlaybackKeys = null;
    this.logger.log('Cleared shared playback keys');
  }

  /**
   * Import an existing playback key pair to be used as shared keys
   * @param name Name for the key pair in AWS IVS
   * @param privateKeyContent The private key content (PEM format)
   * @returns Import result with key pair details
   */
  async importSharedPlaybackKeyPair(
    name: string,
    privateKeyContent: string,
  ): Promise<{
    success: boolean;
    keyPairId?: string;
    arn?: string;
    error?: string;
  }> {
    try {
      // Extract public key from private key
      const publicKey = this.extractPublicKeyFromPrivateKey(privateKeyContent);

      if (!publicKey) {
        return {
          success: false,
          error: 'Failed to extract public key from private key',
        };
      }

      // First, check if a key pair with this public key already exists
      const existingKeyPair = await this.findExistingKeyPairByPublicKey(publicKey);

      if (existingKeyPair) {
        this.logger.log(`Found existing playback key pair: ${existingKeyPair.name} (${existingKeyPair.keyPairId})`);

        // Configure as shared keys using existing key pair
        this.configureSharedPlaybackKeys(
          existingKeyPair.keyPairId,
          privateKeyContent,
          publicKey,
          existingKeyPair.arn,
        );

        return {
          success: true,
          keyPairId: existingKeyPair.keyPairId,
          arn: existingKeyPair.arn,
        };
      }

      // If no existing key pair found, import the public key to AWS IVS
      const command = new ImportPlaybackKeyPairCommand({
        name: name,
        publicKeyMaterial: publicKey,
      });

      const response = await this.ivsClient.send(command);

      if (!response.keyPair || !response.keyPair.arn) {
        return {
          success: false,
          error: 'Failed to import playback key pair - no response data',
        };
      }

      const keyPairId = response.keyPair.arn.split('/').pop() || '';

      if (!keyPairId) {
        return {
          success: false,
          error: 'Failed to extract key pair ID from ARN',
        };
      }

      // Configure as shared keys
      this.configureSharedPlaybackKeys(
        keyPairId,
        privateKeyContent,
        publicKey,
        response.keyPair.arn,
      );

      this.logger.log(`Successfully imported and configured shared playback key pair: ${name} (${keyPairId})`);

      return {
        success: true,
        keyPairId,
        arn: response.keyPair.arn,
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';

      // Check if it's a duplicate key error
      if (errorMessage.includes('already exists with the given publicKeyMaterial')) {
        this.logger.warn('Public key already exists in AWS IVS. Attempting to find and use existing key pair...');

        // Try to find the existing key pair
        const publicKey = this.extractPublicKeyFromPrivateKey(privateKeyContent);
        if (publicKey) {
          const existingKeyPair = await this.findExistingKeyPairByPublicKey(publicKey);
          if (existingKeyPair) {
            this.configureSharedPlaybackKeys(
              existingKeyPair.keyPairId,
              privateKeyContent,
              publicKey,
              existingKeyPair.arn,
            );

            this.logger.log(`Successfully configured existing shared playback key pair: ${existingKeyPair.name} (${existingKeyPair.keyPairId})`);

            return {
              success: true,
              keyPairId: existingKeyPair.keyPairId,
              arn: existingKeyPair.arn,
            };
          }
        }
      }

      this.logger.error(`Failed to import shared playback key pair: ${errorMessage}`);

      return {
        success: false,
        error: errorMessage,
      };
    }
  }

  /**
   * Find an existing key pair by comparing public keys
   * @param targetPublicKey The public key to search for
   * @returns Existing key pair details or null if not found
   */
  private async findExistingKeyPairByPublicKey(targetPublicKey: string): Promise<{
    arn: string;
    keyPairId: string;
    name: string;
  } | null> {
    try {
      const keyPairs = await this.listPlaybackKeyPairs();

      // Since AWS doesn't return public key material, we'll look for key pairs
      // that might match based on naming patterns or use the first available one
      // This is a limitation of AWS IVS API - it doesn't expose public key material

      for (const keyPair of keyPairs) {
        if (keyPair.arn && keyPair.name) {
          const keyPairId = keyPair.arn.split('/').pop() || '';
          if (keyPairId) {
            // For now, we'll assume any existing key pair could be the one we want
            // In a production environment, you might want to use specific naming conventions
            // or store the mapping elsewhere
            return {
              arn: keyPair.arn,
              keyPairId,
              name: keyPair.name,
            };
          }
        }
      }

      return null;
    } catch (error) {
      this.logger.error(`Error finding existing key pair: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return null;
    }
  }

  /**
   * Extract public key from private key using Node.js crypto
   * @param privateKeyContent The private key content (PEM format)
   * @returns Public key in PEM format or null if failed
   */
  private extractPublicKeyFromPrivateKey(privateKeyContent: string): string | null {
    try {
      // Create a KeyObject from the private key
      const privateKeyObject = crypto.createPrivateKey(privateKeyContent);

      // Extract the public key
      const publicKeyObject = crypto.createPublicKey(privateKeyObject);

      // Export as PEM
      const publicKey = publicKeyObject.export({
        type: 'spki',
        format: 'pem',
      }) as string;

      return publicKey.trim();
    } catch (error) {
      this.logger.error(`Failed to extract public key from private key: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return null;
    }
  }
}
