export default () => ({
  port: parseInt(process.env.PORT || '3000'),
  nodeEnv: process.env.NODE_ENV || 'development',
  apiPrefix: process.env.API_PREFIX || 'api',

  neo4j: {
    uri: process.env.NEO4J_URI || 'bolt://localhost:7687',
    username: process.env.NEO4J_USERNAME || 'neo4j',
    password: process.env.NEO4J_PASSWORD || 'password',
    database: process.env.NEO4J_DATABASE || 'video-stream',
  },

  stripe: {
    secretKey: process.env.STRIPE_SECRET_KEY || '',
    webhookSecret: process.env.STRIPE_WEBHOOK_SECRET || '',
  },

  keycloak: {
    authServerUrl: process.env.KEYCLOAK_AUTH_SERVER_URL || '',
    realm: process.env.KEYCLOAK_REALM || '',
    clientId: process.env.KEYCLOAK_CLIENT_ID || '',
    secret: process.env.KEYCLOAK_SECRET || '',
    clientIdAdmin: process.env.KEYCLOAK_ADMIN_CLIENT_ID || '',
    secretAdmin: process.env.KEYCLOAK_ADMIN_SECRET || '',
    publicKey: process.env.KEYCLOAK_PUBLIC_KEY || '',
    disableSslVerification:
      process.env.KEYCLOAK_DISABLE_SSL_VERIFICATION === 'true',
  },

  bunnynet: {
    apiKey: process.env.BUNNYNET_API_KEY || '',
    storageZoneName: process.env.BUNNYNET_STORAGE_ZONE_NAME || '',
    storageZoneRegion: process.env.BUNNYNET_STORAGE_ZONE_REGION || '',
    pullZoneUrl: process.env.BUNNYNET_PULL_ZONE_URL || '',
  },

  aws: {
    region: process.env.AWS_REGION || 'us-east-1',
    accessKeyId: process.env.AWS_ACCESS_KEY_ID || '',
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || '',
  },

  ivs: {
    sharedPlaybackPrivateKey: process.env.IVS_SHARED_PLAYBACK_PRIVATE_KEY?.replace(/\\n/g, '\n') || '',
    sharedPlaybackKeyName: process.env.IVS_SHARED_PLAYBACK_KEY_NAME || 'shared-playback-keys',

    // Playback restriction policy configuration
    playbackRestriction: {
      enabled: process.env.IVS_PLAYBACK_RESTRICTION_ENABLED === 'true',
      policyName: process.env.IVS_PLAYBACK_RESTRICTION_POLICY_NAME || 'default-restriction-policy',
      allowedCountries: process.env.IVS_PLAYBACK_RESTRICTION_ALLOWED_COUNTRIES?.split(',') || [],
      allowedOrigins: process.env.IVS_PLAYBACK_RESTRICTION_ALLOWED_ORIGINS?.split(',') || ['*'],
      strictOriginEnforcement: process.env.IVS_PLAYBACK_RESTRICTION_STRICT_ORIGIN === 'true',
    },
  },
  frontend: {
    url: process.env.FRONTEND_DOMAIN || 'http://localhost:3001',
  },

  commission: {
    platformPercentage: parseFloat(process.env.PLATFORM_COMMISSION_PERCENTAGE || '20'),
    creatorPercentage: parseFloat(process.env.CREATOR_COMMISSION_PERCENTAGE || '80'),
    payoutThreshold: parseFloat(process.env.PAYOUT_THRESHOLD || '50'), // Minimum amount for payout
    payoutDelayDays: parseInt(process.env.PAYOUT_DELAY_DAYS || '7'), // Days before funds are available
  },

  paymentProcessing: {
    // Stripe fees: 2.9% + $0.30 per transaction (in cents)
    stripePercentageFee: parseFloat(process.env.STRIPE_PERCENTAGE_FEE || '2.9'),
    stripeFixedFeeCents: parseInt(process.env.STRIPE_FIXED_FEE_CENTS || '30'),
    // PayPal fees: 2.9% + $0.30 per transaction
    paypalPercentageFee: parseFloat(process.env.PAYPAL_PERCENTAGE_FEE || '2.9'),
    paypalFixedFeeCents: parseInt(process.env.PAYPAL_FIXED_FEE_CENTS || '30'),
  },

  streaming: {
    // AWS IVS charges per participant-hour based on region
    // Europe (Frankfurt/Ireland) pricing
    region: process.env.AWS_REGION || 'eu-west-1',
    regionName: process.env.AWS_REGION_NAME || 'Europe',

    // Cost per participant-hour for different channel types
    // ADVANCED_HD: Better quality, higher cost
    // STANDARD: Standard quality, lower cost
    advancedHdRatePerParticipantHour: parseFloat(process.env.IVS_ADVANCED_HD_RATE_PER_PARTICIPANT_HOUR || '0.0225'),
    standardRatePerParticipantHour: parseFloat(process.env.IVS_STANDARD_RATE_PER_PARTICIPANT_HOUR || '0.0080'),

    // Channel configuration
    defaultChannelType: process.env.DEFAULT_IVS_CHANNEL_TYPE || 'ADVANCED_HD',

    // Whether to apply streaming charges based on participant-hours
    applyStreamingCharges: process.env.APPLY_STREAMING_CHARGES === 'true' || true,

    // Minimum charge threshold (AWS doesn't charge for very small usage)
    minimumChargeThreshold: parseFloat(process.env.IVS_MINIMUM_CHARGE_THRESHOLD || '0.01'),
  },
});
